/**
 * Billing calculation utilities for backend
 */

/**
 * Calculate total fees based on billing type
 * @param rate The rate (stored as daily rate)
 * @param totalDays Total days allocated
 * @param billingType 'daily' or 'hourly'
 * @param hoursPerDay Hours per day for hourly billing
 * @returns Total fees
 */
export function calculateTotalFeesWithBilling(
  rate: number,
  totalDays: number,
  billingType: 'daily' | 'hourly',
  hoursPerDay: number
): number {
  if (billingType === 'daily') {
    // Daily billing: rate * days
    return rate * totalDays;
  } else {
    // Hourly billing: rate * (days * hours per day)
    const totalHours = totalDays * hoursPerDay;
    return rate * totalHours;
  }
}

/**
 * Convert rate between daily and hourly
 * @param currentRate The current rate
 * @param fromType Current billing type
 * @param toType Target billing type
 * @param hoursPerDay Hours per day for conversion
 * @returns Converted rate
 */
export function convertRate(
  currentRate: number,
  fromType: 'daily' | 'hourly',
  toType: 'daily' | 'hourly',
  hoursPerDay: number
): number {
  if (fromType === toType) return currentRate;
  
  if (toType === 'hourly') {
    // Converting from daily to hourly
    return currentRate / hoursPerDay;
  } else {
    // Converting from hourly to daily
    return currentRate * hoursPerDay;
  }
}