import React, { useState } from "react";
import {
  AllocationWithTotals,
  ProjectTotals,
} from "../../hooks/useEstimateStaffManagement";
import { formatCurrency } from "./utils";
import FloatingFinancialSummary from "./FloatingFinancialSummary";
import WeeklyEffortHistogram from "./WeeklyEffortHistogram";
import TeamMembersTable from "./TeamMembersTable";
import TimeAllocationGridEnhanced from "./TimeAllocationGridEnhanced";
import FinancialDetailSection from "./FinancialDetailSection";
import RateDisplayControls, {
  RateDisplayMode,
  HoursPerDay,
} from "./RateDisplayControls";
import useWeeks from "../../hooks/useWeeks";

interface EstimateTableProps {
  // Accept calculated totals as props instead of base allocations
  allocationsWithTotals: AllocationWithTotals[];
  projectTotals: ProjectTotals;
  startDate?: Date; // Optional for now, needed for week generation
  endDate?: Date; // Optional for now, needed for week generation
  onAllocationChange: (
    internalId: string,
    weekIdentifier: string,
    days: number
  ) => void;
  onRateChange: (internalId: string, newRate: number) => void;
  onTriggerAddStaff: () => void; // Renamed prop for triggering the modal
  onRemoveStaff: (internalId: string) => void;
  onReorderAllocations?: (orderedAllocationIds: string[]) => void;
  onDiscountTypeChange?: (type: "percentage" | "amount" | "none") => void;
  onDiscountValueChange?: (value: number) => void;
  isReadOnly?: boolean; // Added prop to disable editing
  estimateId: string; // Required for the enhanced grid API calls
  billingType: 'daily' | 'hourly'; // Billing type from form state
  hoursPerDay: number; // Hours per day from form state
  onBillingTypeChange: (type: 'daily' | 'hourly') => void; // Handler for billing type changes
  onHoursPerDayChange: (hours: number) => void; // Handler for hours per day changes
}

/**
 * Main component for displaying and editing project estimates
 */
const EstimateTable: React.FC<EstimateTableProps> = ({
  allocationsWithTotals,
  projectTotals,
  startDate,
  endDate,
  onAllocationChange,
  onRateChange,
  onTriggerAddStaff,
  onRemoveStaff,
  onReorderAllocations,
  onDiscountTypeChange,
  onDiscountValueChange,
  isReadOnly = false,
  estimateId,
  billingType,
  hoursPerDay,
  onBillingTypeChange,
  onHoursPerDayChange,
}) => {
  // Display unit state for the time allocation grid
  const [displayUnit, setDisplayUnit] = useState<'hours' | 'days'>('days');

  // Generate week information based on start/end dates using our custom hook
  const weeks = useWeeks(startDate, endDate);

  return (
    <div>
      {/* Floating financial summary */}
      <FloatingFinancialSummary projectTotals={projectTotals} />

      {/* Budget Summary has been moved to FinancialDetailSection */}

      {/* Main layout container */}
      <div className="w-full">
        {/* Rate Display Controls */}
        {allocationsWithTotals.length > 0 && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
            <RateDisplayControls
              rateDisplayMode={billingType}
              onRateDisplayModeChange={onBillingTypeChange}
              hoursPerDay={hoursPerDay as HoursPerDay}
              onHoursPerDayChange={(hours) => onHoursPerDayChange(hours)}
            />
          </div>
        )}

        {/* Team Members & Rates Panel */}
        <div className="mb-4">
          <TeamMembersTable
            allocationsWithTotals={allocationsWithTotals}
            projectTotals={projectTotals}
            onRateChange={onRateChange}
            onTriggerAddStaff={onTriggerAddStaff}
            onRemoveStaff={onRemoveStaff}
            isReadOnly={isReadOnly}
            rateDisplayMode={billingType}
            hoursPerDay={hoursPerDay as HoursPerDay}
          />

          {/* Time Allocation Grid */}
          {allocationsWithTotals.length > 0 && weeks.length > 0 && (
            <div className="mt-4">
              <TimeAllocationGridEnhanced
                allocationsWithTotals={allocationsWithTotals}
                weeks={weeks}
                onAllocationChange={onAllocationChange}
                isReadOnly={isReadOnly}
                estimateId={estimateId}
                displayUnit={displayUnit}
                onDisplayUnitChange={setDisplayUnit}
                onReorderAllocations={onReorderAllocations}
              />
            </div>
          )}
        </div>
      </div>

      {/* Financial Detail */}
      {allocationsWithTotals.length > 0 && (
        <FinancialDetailSection
          allocationsWithTotals={allocationsWithTotals}
          projectTotals={projectTotals}
          onDiscountTypeChange={onDiscountTypeChange}
          onDiscountValueChange={onDiscountValueChange}
        />
      )}

      {/* Team Utilisation Metrics */}
      {allocationsWithTotals.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mt-4">
          <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
            Team Utilisation
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Role Distribution */}
            <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Role Distribution
              </div>

              {(() => {
                // Group by role and calculate days per role
                const roleDistribution = allocationsWithTotals.reduce(
                  (acc, staff) => {
                    const role = staff.projectRole || "Staff";
                    if (!acc[role]) {
                      acc[role] = {
                        days: 0,
                        count: 0,
                      };
                    }
                    acc[role].days += staff.totalDays;
                    acc[role].count += 1;
                    return acc;
                  },
                  {} as Record<string, { days: number; count: number }>
                );

                // Calculate percentage of total days
                const totalDays = projectTotals.totalDays;

                return (
                  <div className="space-y-2">
                    {Object.entries(roleDistribution).map(
                      ([role, data]: [
                        string,
                        { days: number; count: number }
                      ]) => {
                        const percentage =
                          totalDays > 0 ? (data.days / totalDays) * 100 : 0;
                        return (
                          <div key={role} className="flex flex-col">
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                {role}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {data.days.toFixed(1)} days (
                                {percentage.toFixed(0)}%)
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                              <div
                                className="h-1.5 rounded-full bg-blue-500 dark:bg-blue-400"
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                );
              })()}
            </div>

            {/* Staff Workload */}
            <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Staff Workload
              </div>

              {(() => {
                // Sort allocations by days for visualization
                const sortedAllocations = [...allocationsWithTotals].sort(
                  (a, b) => b.totalDays - a.totalDays
                );

                // Find max days for scaling
                const maxDays = Math.max(
                  ...sortedAllocations.map((s) => s.totalDays),
                  5
                );

                return (
                  <div className="space-y-2">
                    {sortedAllocations.map((staff) => {
                      const percentage =
                        maxDays > 0 ? (staff.totalDays / maxDays) * 100 : 0;

                      return (
                        <div key={staff.internalId} className="flex flex-col">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                              {staff.firstName || ""}{" "}
                              {staff.lastName ? staff.lastName.charAt(0) : ""}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {staff.totalDays.toFixed(1)} days
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                            <div
                              className="h-1.5 rounded-full bg-emerald-500 dark:bg-emerald-400"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </div>
          </div>

          {/* Weekly Effort Histogram */}
          <div
            className="mt-4 p-4 bg-gray-50 dark:bg-gray-800/60 rounded-lg overflow-visible shadow-sm border border-gray-200 dark:border-gray-700"
            style={{ contain: "none" }}
          >
            <div className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-4">
              Weekly Effort Histogram
            </div>

            {/* Using the WeeklyEffortHistogram component - with overflow visible */}
            {(() => {
              // Get all staff with consistent colors for each person
              const staffMembers = allocationsWithTotals.map(
                (staff, index) => ({
                  internalId: staff.internalId,
                  name: `${staff.firstName || ""} ${
                    staff.lastName ? staff.lastName.charAt(0) : ""
                  }`,
                  initials: `${staff.firstName ? staff.firstName.charAt(0) : ""}${
                    staff.lastName ? staff.lastName.charAt(0) : ""
                  }`,
                  role: staff.projectRole || "Staff",
                  avatarUrl: staff.avatarUrl,
                })
              );

              // Calculate weekly allocations with staff breakdown
              const weeklyData = weeks.map((week) => {
                // Get allocations for each staff member in this week
                const staffAllocations = staffMembers
                  .map((staff) => {
                    const staffAlloc = allocationsWithTotals.find(
                      (s) => s.internalId === staff.internalId
                    );
                    return {
                      ...staff,
                      days: staffAlloc
                        ? staffAlloc.weeklyAllocation[week.identifier] || 0
                        : 0,
                    };
                  })
                  .filter((staff) => staff.days > 0);

                const total = staffAllocations.reduce(
                  (sum, staff) => sum + staff.days,
                  0
                );

                return {
                  week: week.shortLabel,
                  identifier: week.identifier,
                  hasAlternatingBackground: week.hasAlternatingBackground,
                  total,
                  staffAllocations,
                };
              });

              return (
                <WeeklyEffortHistogram
                  weeklyData={weeklyData}
                  staffMembers={staffMembers}
                />
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
};

export default EstimateTable;
