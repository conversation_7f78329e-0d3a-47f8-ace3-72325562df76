import React, { useState, useEffect, useMemo } from "react";
import { format, parseISO } from "date-fns";
import { DraftEstimateSummary } from "../../../../types/api";
import { formatCurrency } from "../../Estimate/utils/index";
import { useQuery } from "react-query";
import { getLinkedDealsForEstimate } from "../../../api/crm";

interface EstimateLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateType: "internal";
  draftEstimates: DraftEstimateSummary[];
  onLinkEstimate: (estimateId: string, type: "internal") => void;
  isLoading: boolean;
  error?: string;
  dealName?: string;
  dealCompanyName?: string;
}

/**
 * Calculate similarity score between two strings
 * Returns a score between 0 and 1, where 1 is an exact match
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;
  
  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();
  
  // Exact match
  if (s1 === s2) return 1;
  
  // Check if one contains the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;
  
  // Check for common words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  const commonWords = words1.filter(word => words2.includes(word));
  const commonScore = commonWords.length / Math.max(words1.length, words2.length);
  
  // Simple character-based similarity
  const maxLen = Math.max(s1.length, s2.length);
  let matches = 0;
  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) matches++;
  }
  const charScore = matches / maxLen;
  
  // Return weighted average
  return commonScore * 0.7 + charScore * 0.3;
};

/**
 * Modal for linking estimates to a deal
 */
const EstimateLinkModal: React.FC<EstimateLinkModalProps> = ({
  isOpen,
  onClose,
  estimateType,
  draftEstimates,
  onLinkEstimate,
  isLoading,
  error,
  dealName,
  dealCompanyName,
}) => {
  const [selectedEstimateId, setSelectedEstimateId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [localError, setLocalError] = useState<string | null>(null);
  const [linkedEstimateIds, setLinkedEstimateIds] = useState<Set<string>>(new Set());

  // Fetch linked deals for all estimates to filter out already linked ones
  useEffect(() => {
    if (isOpen && draftEstimates.length > 0) {
      const fetchLinkedDeals = async () => {
        const linkedIds = new Set<string>();
        
        // Fetch linked deals for each estimate
        await Promise.all(
          draftEstimates.map(async (estimate) => {
            try {
              const linkedDeals = await getLinkedDealsForEstimate(estimate.uuid, "internal");
              if (linkedDeals && linkedDeals.length > 0) {
                linkedIds.add(estimate.uuid);
              }
            } catch (err) {
              console.error(`Failed to fetch linked deals for estimate ${estimate.uuid}:`, err);
            }
          })
        );
        
        setLinkedEstimateIds(linkedIds);
      };
      
      fetchLinkedDeals();
    }
  }, [isOpen, draftEstimates]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedEstimateId("");
      setSearchTerm("");
      setLocalError(null);
    }
  }, [isOpen]);

  // Format date to Australian format (DD/MM/YYYY)
  const formatDate = (dateString: string): string => {
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Filter out estimates that are already linked to other deals
  const availableEstimates = useMemo(() => {
    return draftEstimates.filter(estimate => !linkedEstimateIds.has(estimate.uuid));
  }, [draftEstimates, linkedEstimateIds]);

  // Calculate suggested matches based on similarity
  const suggestedMatches = useMemo(() => {
    if (!dealName && !dealCompanyName) return [];
    if (availableEstimates.length === 0) return [];
    
    const estimatesWithScores = availableEstimates.map(estimate => {
      let maxScore = 0;
      
      // Calculate similarity with deal name
      if (dealName) {
        const projectScore = calculateSimilarity(dealName, estimate.projectName || "");
        const clientScore = calculateSimilarity(dealName, estimate.clientName);
        maxScore = Math.max(projectScore, clientScore);
      }
      
      // Calculate similarity with company name
      if (dealCompanyName) {
        const companyScore = calculateSimilarity(dealCompanyName, estimate.clientName);
        maxScore = Math.max(maxScore, companyScore);
      }
      
      return { estimate, score: maxScore };
    });
    
    // Sort by score and take top 3 matches with score > 0.3
    return estimatesWithScores
      .filter(item => item.score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(item => item.estimate);
  }, [dealName, dealCompanyName, availableEstimates]);

  // Filter draft estimates based on search term
  const filteredDraftEstimates = useMemo(() => {
    // If no search term, show available estimates except suggested matches
    if (!searchTerm) {
      const suggestedIds = new Set(suggestedMatches.map(e => e.uuid));
      return availableEstimates.filter(estimate => !suggestedIds.has(estimate.uuid));
    }
    
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    return availableEstimates.filter((estimate) => {
      return (
        estimate.clientName.toLowerCase().includes(lowerCaseSearchTerm) ||
        (estimate.projectName &&
          estimate.projectName.toLowerCase().includes(lowerCaseSearchTerm)) ||
        estimate.status.toLowerCase().includes(lowerCaseSearchTerm)
      );
    });
  }, [availableEstimates, searchTerm, suggestedMatches]);

  // Handle linking the selected estimate
  const handleLinkEstimate = () => {
    if (!selectedEstimateId) {
      setLocalError("Please select an estimate");
      return;
    }
    
    onLinkEstimate(selectedEstimateId, 'internal');
  };

  // Get selected estimate details
  const selectedEstimate = draftEstimates.find(e => e.uuid === selectedEstimateId);

  // Calculate estimate total for display
  const calculateEstimateTotal = (estimate: DraftEstimateSummary) => {
    console.log('EstimateLinkModal - calculateEstimateTotal for estimate:', {
      uuid: estimate.uuid,
      projectName: estimate.projectName,
      totalFees: estimate.totalFees,
      allocations: estimate.allocations
    });
    
    // If totalFees is already available, use it
    if (estimate.totalFees !== undefined) {
      return estimate.totalFees;
    }
    
    // Otherwise calculate from allocations if available
    if (estimate.allocations && Array.isArray(estimate.allocations)) {
      let total = 0;
      estimate.allocations.forEach((allocation: any) => {
        const totalDays = allocation.timeAllocations?.reduce(
          (sum: number, ta: any) => sum + (ta.days || 0),
          0
        ) || 0;
        total += (allocation.rateProposedDaily || 0) * totalDays;
      });
      return total;
    }
    
    return 0;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Link to Estimate
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {(error || localError) && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-md">
              {error || localError}
            </div>
          )}

          {/* Deal being linked */}
          {(dealName || dealCompanyName) && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                Deal to link:
              </div>
              <div className="font-medium text-gray-900 dark:text-white">
                {dealName || "Unnamed Deal"}
              </div>
              {dealCompanyName && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {dealCompanyName}
                </div>
              )}
            </div>
          )}

          {/* Suggested matches */}
          {suggestedMatches.length > 0 && !searchTerm && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Suggested Matches
              </label>
              <div className="space-y-2">
                {suggestedMatches.map((estimate) => {
                  const total = calculateEstimateTotal(estimate);
                  return (
                    <div
                      key={estimate.uuid}
                      className={`p-3 border rounded-md cursor-pointer transition-colors relative ${
                        selectedEstimateId === estimate.uuid
                          ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 dark:border-emerald-700"
                          : "border-emerald-300 dark:border-emerald-600 hover:bg-emerald-50 dark:hover:bg-emerald-900/10"
                      }`}
                      onClick={() => {
                        setSelectedEstimateId(estimate.uuid);
                        setLocalError(null);
                      }}
                    >
                      <div className="absolute top-2 right-2">
                        <span className="text-xs bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 px-2 py-1 rounded">
                          Suggested
                        </span>
                      </div>
                      <div className="font-medium text-gray-900 dark:text-white pr-20">
                        {estimate.projectName || "Unnamed Project"}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex justify-between items-center">
                          <span>{estimate.clientName}</span>
                          <span className="font-medium">{formatCurrency(total)}</span>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <span className="capitalize">{estimate.status}</span>
                          <span className="text-xs">{formatDate(estimate.updatedAt)}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 italic">
                These suggestions are based on name similarity
              </div>
            </div>
          )}

          <div className="mb-4">
            <label
              htmlFor="search-estimates"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {searchTerm ? 'Search Results' : 'All Available Estimates'}
            </label>
            <input
              type="text"
              id="search-estimates"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
              placeholder="Search by client, project, or status..."
            />
          </div>

          {filteredDraftEstimates.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              {searchTerm 
                ? "No estimates found matching your search." 
                : availableEstimates.length === 0 
                  ? "No available estimates. All estimates are already linked to deals." 
                  : "No additional estimates available."}
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredDraftEstimates.map((estimate) => {
                const total = calculateEstimateTotal(estimate);
                return (
                  <div
                    key={estimate.uuid}
                    className={`p-3 border rounded-md cursor-pointer transition-colors ${
                      selectedEstimateId === estimate.uuid
                        ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 dark:border-emerald-700"
                        : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                    }`}
                    onClick={() => {
                      setSelectedEstimateId(estimate.uuid);
                      setLocalError(null);
                    }}
                  >
                    <div className="font-medium text-gray-900 dark:text-white">
                      {estimate.projectName || "Unnamed Project"}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex justify-between items-center">
                        <span>{estimate.clientName}</span>
                        <span className="font-medium">{formatCurrency(total)}</span>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="capitalize">{estimate.status}</span>
                        <span className="text-xs">{formatDate(estimate.updatedAt)}</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Selected estimate details */}
          {selectedEstimate && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Selected Estimate Details
              </h4>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <p>
                  <span className="font-medium">Project:</span>{" "}
                  {selectedEstimate.projectName || "Unnamed Project"}
                </p>
                <p>
                  <span className="font-medium">Client:</span>{" "}
                  {selectedEstimate.clientName}
                </p>
                {selectedEstimate.startDate && selectedEstimate.endDate && (
                  <p>
                    <span className="font-medium">Date Range:</span>{" "}
                    {formatDate(selectedEstimate.startDate)} - {formatDate(selectedEstimate.endDate)}
                  </p>
                )}
                <p>
                  <span className="font-medium">Total Value:</span>{" "}
                  {formatCurrency(calculateEstimateTotal(selectedEstimate))}
                </p>
              </div>
              <div className="mt-3 p-2 bg-amber-100 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-800 rounded">
                <p className="text-xs text-amber-800 dark:text-amber-200 font-medium">
                  ⚠️ Important: This action is permanent
                </p>
                <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  Once linked, this estimate cannot be unlinked from the deal. The deal's value, dates, 
                  and payment terms will be controlled by this estimate.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleLinkEstimate}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!selectedEstimateId || isLoading}
          >
            {isLoading ? "Linking..." : "Link Estimate"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EstimateLinkModal;
