import React, { useState, useEffect } from "react";
import { useQueryClient } from "react-query";
import { getLinkedDealsForEstimate } from "../../../api/crm";
import { formatCurrency } from "../../Estimate/utils/index";
import { format } from "date-fns";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";

interface LinkedDealModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateId: string;
  estimateType: "internal" | "harvest";
}

/**
 * Modal for viewing and managing a linked deal
 */
const LinkedDealModal: React.FC<LinkedDealModalProps> = ({
  isOpen,
  onClose,
  estimateId,
  estimateType,
}) => {
  const [linkedDeal, setLinkedDeal] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch linked deal when modal opens
  useEffect(() => {
    if (isOpen && estimateId) {
      fetchLinkedDeal();
    }
  }, [isOpen, estimateId]);

  // Fetch linked deal data
  const fetchLinkedDeal = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const linkedDeals = await getLinkedDealsForEstimate(estimateId, estimateType);
      if (linkedDeals && linkedDeals.length > 0) {
        setLinkedDeal(linkedDeals[0]);
      } else {
        setError("No linked deal found");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch linked deal");
    } finally {
      setLoading(false);
    }
  };

  // Unlinking is no longer supported - linking is permanent

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Linked Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-md">
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <svg
                className="animate-spin h-8 w-8 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : linkedDeal ? (
            <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-md p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {linkedDeal.name}
                  </h3>
                  {linkedDeal.company && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {linkedDeal.company.name}
                    </p>
                  )}
                </div>
                <span className="text-xs bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-200 px-2 py-0.5 rounded-full capitalize">
                  {linkedDeal.stage}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Value
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {linkedDeal.value
                      ? formatCurrency(linkedDeal.value, linkedDeal.currency)
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Probability
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {linkedDeal.probability !== undefined
                      ? `${Math.round(linkedDeal.probability * 100)}%`
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Expected Close Date
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {linkedDeal.expectedCloseDate
                      ? format(new Date(linkedDeal.expectedCloseDate), "MMM d, yyyy")
                      : "Not set"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Created
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {format(new Date(linkedDeal.createdAt), "MMM d, yyyy")}
                  </p>
                </div>
              </div>

              {linkedDeal.description && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Description
                  </p>
                  <p className="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                    {linkedDeal.description}
                  </p>
                </div>
              )}

              <div className="flex justify-between items-center mt-4 pt-3 border-t border-emerald-200 dark:border-emerald-800">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Linked on {linkedDeal.linkedAt ? format(new Date(linkedDeal.linkedAt), "MMM d, yyyy") : "Unknown date"}
                </div>
                <div className="flex space-x-2">
                  <a
                    href={`/crm/deals/${linkedDeal.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-xs text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-300"
                  >
                    <ArrowTopRightOnSquareIcon className="w-3.5 h-3.5 mr-1" />
                    View Deal
                  </a>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No linked deal found.
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkedDealModal;
