# Billing Models - Technical Implementation Guide

## Overview

This document provides the technical implementation details for adding Daily and Hourly billing support to the Upstream estimates system. It consolidates the valid technical requirements from our planning documents.

## Database Changes

### Minimal Schema Updates

```sql
-- Add billing type to estimate table
ALTER TABLE estimate ADD COLUMN billing_type TEXT DEFAULT 'daily' 
  CHECK(billing_type IN ('daily', 'hourly'));

-- Note: We do NOT need separate hourly rate storage
-- We'll store rates in whatever format they're entered and convert as needed
```

**Why this approach:**
- Minimal disruption to existing data
- All existing estimates default to 'daily' (current behavior)
- Simple rollback if needed

## Data Model Updates

### TypeScript Interfaces

```typescript
// Update Estimate interface
interface Estimate {
  // Existing fields...
  billingType: 'daily' | 'hourly';
  hoursPerDay: number; // Already exists, used for both billing types
}

// Add rate storage type to allocations
interface StaffAllocation {
  // Existing fields...
  rateType: 'daily' | 'hourly'; // How the rate was entered
  proposedRate: number; // The actual entered value
}
```

## Implementation Areas

### 1. Backend Services

**Files to update:**
- `src/api/repositories/estimate-repository.ts` - Add billing type field
- `src/api/repositories/estimate-allocation-repository.ts` - Add rate type handling
- `src/services/estimate-calculation-service.ts` - New calculation logic
- `src/api/routes/estimates.ts` - Update API to handle billing type

**Core calculation logic:**
```typescript
function calculateTotal(
  allocation: StaffAllocation, 
  estimate: Estimate
): number {
  if (estimate.billingType === 'hourly' && allocation.rateType === 'hourly') {
    // Hourly billing: rate × hours
    const hours = allocation.totalDays * estimate.hoursPerDay;
    return allocation.proposedRate * hours;
  } else if (estimate.billingType === 'daily' && allocation.rateType === 'daily') {
    // Daily billing: rate × days
    return allocation.proposedRate * allocation.totalDays;
  } else {
    // Handle rate conversion if billing type changed
    return handleRateConversion(allocation, estimate);
  }
}
```

### 2. Frontend Components

**Components to update:**
- `EstimateConfigurationForm` - Add billing type selector
- `RateDisplayControls` - Remove or repurpose (consolidate to one toggle)
- `TeamMembersTable` - Update rate entry based on billing type
- `TimeAllocationGridEnhanced` - Remove local hours/days toggle
- `EstimateTable` - Pass billing type and hours/day to child components

**Key UI changes:**
1. Remove the time allocation hours/days toggle
2. Make the pricing model toggle control both display and calculations
3. Update rate input placeholders based on billing type

### 3. Rate Conversion Logic

**When user toggles billing type:**
```typescript
function convertRate(
  currentRate: number,
  fromType: 'daily' | 'hourly',
  toType: 'daily' | 'hourly',
  hoursPerDay: number
): number {
  if (fromType === toType) return currentRate;
  
  if (toType === 'hourly') {
    // Daily → Hourly
    return currentRate / hoursPerDay;
  } else {
    // Hourly → Daily
    return currentRate * hoursPerDay;
  }
}
```

### 4. Harvest Integration Considerations

**Important:** Harvest API returns hourly rates, not daily rates.

```typescript
// When importing from Harvest
const harvestUser = await harvest.getUser(userId);
const hourlyRate = harvestUser.default_hourly_rate;

// Convert based on estimate billing type
if (estimate.billingType === 'daily') {
  allocation.proposedRate = hourlyRate * estimate.hoursPerDay;
  allocation.rateType = 'daily';
} else {
  allocation.proposedRate = hourlyRate;
  allocation.rateType = 'hourly';
}
```

## Testing Requirements

### Critical Test Scenarios

```typescript
const testScenarios = [
  // Daily billing tests
  { billing: 'daily', rate: 1000, rateType: 'daily', days: 5, hoursPerDay: 8, expected: 5000 },
  { billing: 'daily', rate: 1000, rateType: 'daily', days: 5, hoursPerDay: 7.5, expected: 5000 },
  
  // Hourly billing tests  
  { billing: 'hourly', rate: 125, rateType: 'hourly', days: 5, hoursPerDay: 8, expected: 5000 },
  { billing: 'hourly', rate: 125, rateType: 'hourly', days: 5, hoursPerDay: 7.5, expected: 3750 },
  
  // Conversion tests
  { 
    name: 'Toggle daily to hourly at 8h',
    initial: { billing: 'daily', rate: 1000, rateType: 'daily' },
    toggle: { billing: 'hourly', hoursPerDay: 8 },
    expected: { rate: 125, rateType: 'hourly', total: 'unchanged' }
  }
];
```

### Areas to Test
1. Rate entry and storage
2. Billing type toggling with rate conversion
3. Hours/day changes (affects hourly billing only)
4. Existing estimate compatibility
5. Harvest import with both billing types
6. Calculation accuracy with decimal values

## Migration Strategy

Given the small number of estimates in production:

1. **Deploy the changes**
2. **Set billing type on existing estimates**:
   ```sql
   -- All existing estimates are daily billing
   UPDATE estimate SET billing_type = 'daily' WHERE billing_type IS NULL;
   ```
3. **Manually review** any estimates that might need hourly billing
4. **No complex migration needed** - just set the correct type

## Risks and Mitigations

### Primary Risk: Calculation Errors
- **Mitigation**: Comprehensive test suite before deployment
- **Validation**: Add calculation preview when toggling billing types

### Secondary Risk: User Confusion
- **Mitigation**: Clear UI labels explaining what each mode does
- **Help text**: "Daily billing: Fixed rate per day" vs "Hourly billing: Rate × actual hours"

## Implementation Order

1. **Backend first**: Add billing type field and calculation logic
2. **Test thoroughly**: Ensure calculations are correct
3. **Update UI**: Add billing type selector and update toggles
4. **Deploy**: With manual verification of key estimates
5. **Monitor**: Watch for any calculation discrepancies

## Clean Up

### Documents to Archive/Delete
1. `BILLING-MODEL-IMPLEMENTATION-PLAN.md` - Over-engineered with unnecessary phases
2. `BILLING-MODEL-SIMPLIFIED-PLAN.md` - Incomplete and confusing
3. `PRICING-CALCULATION-GUIDE.md` - Keep but update to reflect new functionality

### Keep
1. `BILLING-MODELS-FINAL-DESIGN.md` - The business logic reference
2. This document - The technical implementation guide