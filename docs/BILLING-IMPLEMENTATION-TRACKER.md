# Billing Implementation Tracker

## Overview

Implementing global billing type (daily/hourly) for estimates to replace confusing dual toggles.

## Implementation Checklist

### 1. Database Changes

- [x] Create migration to add `billing_type` to estimate table
- [x] Run migration and verify
- [x] Update TypeScript types

**Notes:**
- Created migration 002_add_billing_type.sql
- Added billing_type (daily/hourly) with default 'daily'
- Added hours_per_day with default 8.0 (was hardcoded to 7.5 in utils)
- Migration ran successfully
- Updated DraftEstimate interfaces in src/types/api.ts to include billingType and hoursPerDay

### 2. Backend Changes

- [x] Update estimate repository to handle billing_type
- [x] Update calculation logic for both billing types

**Notes:**
- Updated findAll, findByUuid, create, and update methods in EstimateDraftsRepository
- Added billing_type and hours_per_day to all SELECT queries
- Added defaults: billing_type='daily', hours_per_day=8.0
- Created calculateTotalFeesWithBilling function for billing-aware calculations
- Created convertRate function for rate conversion when toggling
- [ ] Update API endpoints to accept/return billing_type
- [ ] Handle rate conversion logic

### 3. Frontend Changes

- [x] Remove hours/days toggle from TimeAllocationGridEnhanced
- [x] Refactor state management to lift billing type from local to form state
- [x] Update RateDisplayControls to set billing_type (not just display)
- [x] Update TeamMembersTable rate input based on billing_type
- [x] Implement rate conversion when toggling billing types
- [x] Update estimate saving/loading

**Notes:**
- Removed hours/days toggle buttons from TimeAllocationGridEnhanced
- Updated formatDisplayValue to always show days
- Updated handleCellChange to always work with days
- Updated column header to always show "Days"
- Refactored useEstimateFormState to include billingType and hoursPerDay
- Updated EstimatePage to pass billing props down to EstimateTable
- Removed local state from EstimateTable, now uses props from parent
- Added convertAllRatesForBillingType method to useEstimateStaffManagement
- TeamMembersTable already properly handles rate display/conversion
- Updated useEstimateDrafts and useDraftLoading to save/load billing fields

### 4. Testing

- [ ] Test billing type toggling
- [ ] Test calculations for both types
- [ ] Test with different hours/day settings
- [ ] Test data persistence

## Implementation Progress Summary (2025-06-14)

### ✅ Completed

1. **Database Schema**: Added billing_type and hours_per_day columns
2. **TypeScript Types**: Updated interfaces to include billing fields
3. **Backend Repository**: SQL queries include new fields
4. **Frontend State Management**:
   - Refactored to lift billing type from local UI state to form state
   - Billing type is now properly saved and loaded with estimates
   - Rate conversion implemented when toggling billing types
5. **Component Updates**:
   - EstimateTable now receives billing props from parent
   - RateDisplayControls updates actual billing type, not just display
   - TeamMembersTable properly formats rates based on billing type

### ✅ Completed (Final Update)

1. **API Endpoints**: Verified and updated
   - API routes already pass full request body to repository
   - EstimateDraftsRepository already handles billing_type and hours_per_day fields
   - Added billing-aware calculation to repository's totalFees calculation

2. **Calculation Integration**: Completed
   - Created billing calculation utilities for backend
   - Updated repository to use calculateTotalFeesWithBilling
   - Updated frontend staff management hook to use billing-aware calculations

3. **Migration Status**: Confirmed
   - Migration 002_add_billing_type has been successfully executed
   - Database has billing_type and hours_per_day columns

### 🎯 Implementation Complete!

The billing type feature has been fully implemented:
- ✅ Database schema updated
- ✅ Frontend state management refactored
- ✅ Billing type persists with estimates
- ✅ Rate conversion works when toggling
- ✅ Calculations respect billing type
- ✅ UI properly reflects billing mode

### 📝 How It Works

1. **Daily Billing**: Total = Rate × Days
2. **Hourly Billing**: Total = Rate × (Days × Hours/Day)
3. **Rate Conversion**: When toggling, rates convert to maintain same daily value
4. **Persistence**: Billing type and hours/day are saved with each estimate

The system now properly supports both daily and hourly billing models with seamless conversion between them!

## Risks & Concerns

### Identified Risks

1. **Hours/Day Inconsistency**: Utils hardcoded to 7.5h but UI allows 7.5h or 8h
2. **Rate Storage**: All rates currently stored as daily - need careful migration
3. **Precision**: Using REAL/float for money (accepted risk for now)
4. **Rate Naming Convention**: Database and types use `proposedRateDaily` but we're now supporting hourly rates too
   - Should probably rename to just `proposedRate` but that's a bigger change
   - For now, treating it as just "rate" regardless of type
5. **Cost Rate Calculation**: Cost rates are still daily-only, might need updating for hourly billing
6. **State Management**: RateDisplayControls is currently only managing display state, not actual billing type
   - EstimateTable manages rateDisplayMode as local state
   - Need to lift this to EstimatePage and save with estimate
   - useEstimateFormState doesn't include billingType or hoursPerDay yet

### Implementation Notes

- Starting with database migration
- Will update this document as we progress

## Decision Log

- **2024-06-14**: Confirmed global billing type (no mixed billing needed)
- **2024-06-14**: Keeping REAL storage for now (no integer/cents migration)

## Major Architecture Discovery (2024-06-14)

### The Problem

RateDisplayControls is managing `rateDisplayMode` as **local UI state** in EstimateTable, not as a core estimate property. This means:
- Billing type is NOT being saved with estimates
- It resets to default on every page load
- It's treated as a display preference rather than affecting calculations

### Current Architecture

```text
EstimatePage
  └── EstimateTable (manages local rateDisplayMode state)
       ├── RateDisplayControls (UI only)
       ├── TeamMembersTable (uses rateDisplayMode for display)
       └── TimeAllocationGrid
```

### Required Refactoring

1. **State Management**:
   - Add billingType and hoursPerDay to useEstimateFormState hook
   - Pass through props from EstimatePage → EstimateTable → child components
   - Remove local state from EstimateTable

2. **Data Flow**:
   - EstimatePage loads billingType from draft/estimate
   - Passes down through component hierarchy
   - Saves billingType when creating/updating estimates

3. **Rate Conversion**:
   - Implement conversion logic when toggling billing types
   - Update all staff allocation rates
   - Ensure proper recalculation

### Complexity Assessment

This is more complex than initially anticipated because:
- Need to refactor state management across multiple components
- Must update data flow from top-level down
- Requires changes to how estimates are saved/loaded
- Rate conversion needs to happen at the right level

### Next Steps Required

1. Refactor useEstimateFormState to include billing fields
2. Update component props throughout hierarchy
3. Implement rate conversion on toggle
4. Ensure save/load integration works properly
5. Update TeamMembersTable to show correct rate format based on billing type
