# Billing Models - Final Design

## Executive Summary

This document defines the final design for supporting both Daily and Hourly billing models in the Upstream estimates system. After extensive analysis, we've arrived at a simple, flexible solution that mirrors how consultants use spreadsheets.

## Core Business Logic

### The Two Billing Models

1. **Daily Rate Billing**
   - Client pays a fixed rate per day
   - Hours per day setting does NOT affect pricing
   - Example: $1000/day × 5 days = $5000 (whether days are 7.5h or 8h)

2. **Hourly Rate Billing**
   - Client pays for actual hours worked
   - Hours per day setting DOES affect pricing
   - Example: $100/hour × 5 days × 8h/day = $4000
   - Example: $100/hour × 5 days × 7.5h/day = $3750

### Key Design Decisions

1. **Flexible Toggling**: Users can switch between all four combinations:
   - Daily + 8h/day
   - Daily + 7.5h/day
   - Hourly + 8h/day
   - Hourly + 7.5h/day

2. **Rate Storage**: Store the rate in the format it was entered:
   - If user selects daily billing and enters $1000, store as `{value: 1000, type: 'daily'}`
   - If user selects hourly billing and enters $125, store as `{value: 125, type: 'hourly'}`

3. **Rate Conversion**: When switching billing types, convert to maintain effective rate:
   - Daily → Hourly: `hourlyRate = dailyRate ÷ hoursPerDay`
   - Hourly → Daily: `dailyRate = hourlyRate × hoursPerDay`

## Complete Example

Starting with Daily billing at $1000/day, 8h/day:

| Action | Stored Rate | Display | 5-Day Total | Why |
|--------|-------------|---------|-------------|-----|
| Initial | $1000 daily | $1000/day ($125/hr) | $5000 | Daily rate × days |
| Switch to Hourly | $125 hourly | $125/hr ($1000/day) | $5000 | Converted to maintain rate |
| Change to 7.5h | $125 hourly | $125/hr ($937.50/day) | $3750 | Less hours = less cost |
| Switch to Daily | $937.50 daily | $937.50/day ($125/hr) | $4687.50 | Converted at 7.5h rate |
| Change to 8h | $937.50 daily | $937.50/day ($117.19/hr) | $4687.50 | Hours don't affect daily |

## Calculation Rules

```typescript
// Simple, deterministic calculations
function calculateTotal(rate: Rate, days: number, hoursPerDay: number): number {
  if (rate.type === 'daily') {
    return rate.value * days;
  } else {
    return rate.value * days * hoursPerDay;
  }
}

// Rate conversion when switching billing types
function convertRate(rate: Rate, newType: 'daily' | 'hourly', hoursPerDay: number): Rate {
  if (rate.type === newType) return rate;
  
  if (newType === 'hourly') {
    return { value: rate.value / hoursPerDay, type: 'hourly' };
  } else {
    return { value: rate.value * hoursPerDay, type: 'daily' };
  }
}
```

## UI Behavior

1. **Single Toggle**: Consolidate to one pricing model toggle (remove the confusing second toggle in time allocation)

2. **Clear Display**: Always show what mode is active and how it affects calculations:
   - Daily mode: "Total = Rate × Days (hours/day for display only)"
   - Hourly mode: "Total = Rate × Hours (days × hours/day)"

3. **Rate Entry**: 
   - Daily mode: Enter $/day
   - Hourly mode: Enter $/hour

4. **Reference Display**: Show the converted rate for reference:
   - Daily mode: "$1000/day (≈ $125/hr @ 8h/day)"
   - Hourly mode: "$125/hr (= $1000/day @ 8h/day)"

## Why This Works

1. **Matches Mental Model**: Works like a spreadsheet where you can toggle between views
2. **No Lock-In**: Can switch modes to see impact on pricing
3. **Accurate Billing**: Correctly handles both billing types
4. **Simple Logic**: Calculations are straightforward and predictable

## Critical Insight

The key breakthrough was recognizing that hours/day has different meanings:
- **For Daily Billing**: Display reference only (doesn't affect price)
- **For Hourly Billing**: Core part of calculation (directly affects price)

This allows the same toggle to serve both purposes without confusion.